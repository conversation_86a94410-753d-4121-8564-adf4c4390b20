<?xml version="1.0"?>
<COLLADA version="1.4.0" xmlns="http://www.collada.org/2005/11/COLLADASchema">
<asset>
<contributor>
<author>Wings3D Collada Exporter</author>
<authoring_tool>Wings3D 1.5.3 Collada Exporter</authoring_tool>
<comments/>
<copyright/>
<source_data/>
</contributor>
<created>2014-10-08T21:39:06</created>
<modified>2014-10-08T21:39:06</modified>
<unit meter="1.0" name="meter"/>
<up_axis>Y_UP</up_axis>
</asset>
<library_effects><effect id="default-fx" name="default-fx"><profile_COMMON><technique sid="wings3d"><phong><emission><color>0.000000 0.000000 0.000000 1.000000
</color></emission><ambient><color>0.789854 0.813333 0.694044 1.000000
</color></ambient><diffuse><color>0.789854 0.813333 0.694044 1.000000
</color></diffuse><specular><color>0.000000 0.000000 0.000000 1.000000
</color></specular><shininess><float>0.000000</float></shininess></phong></technique></profile_COMMON></effect></library_effects>

<library_materials><material id="default" name="default"><instance_effect url="#default-fx"/></material></library_materials>
<library_geometries><geometry name="Cylinder2-0" id="Cylinder2-0"><mesh><source id="Cylinder2-0-Pos"><float_array id="Cylinder2-0-Pos-array" count="432">0.000000 0.045102 0.057352 0.014255 0.042267 0.057352 0.026340 0.034192 0.057352 0.034415 0.022107 0.057352 0.037250 0.007852 0.057352 0.034415 -0.006403 0.057352 0.026340 -0.018487 0.057352 0.014255 -0.026562 0.057352 0.000000 -0.029398 0.057352 -0.014255 -0.026562 0.057352 -0.026340 -0.018487 0.057352 -0.034415 -0.006403 0.057352 -0.037250 0.007852 0.057352 -0.034415 0.022107 0.057352 -0.026340 0.034192 0.057352 -0.014255 0.042267 0.057352 -0.000000 0.035352 -0.072648 0.010524 0.033259 -0.072648 0.019445 0.027298 -0.072648 0.025407 0.018376 -0.072648 0.027500 0.007852 -0.072648 0.025407 -0.002671 -0.072648 0.019445 -0.011593 -0.072648 0.010524 -0.017554 -0.072648 0.000000 -0.019648 -0.072648 -0.010524 -0.017554 -0.072648 -0.019445 -0.011593 -0.072648 -0.025407 -0.002671 -0.072648 -0.027500 0.007852 -0.072648 -0.025407 0.018376 -0.072648 -0.019445 0.027298 -0.072648 -0.010524 0.033259 -0.072648 0.013870 0.041338 0.057352 0.000000 0.044097 0.057352 -0.013870 0.041338 0.057352 -0.025629 0.033481 0.057352 -0.033485 0.021722 0.057352 -0.036244 0.007852 0.057352 -0.033485 -0.006018 0.057352 -0.025629 -0.017776 0.057352 -0.013870 -0.025633 0.057352 0.000000 -0.028392 0.057352 0.013870 -0.025633 0.057352 0.025629 -0.017776 0.057352 0.033485 -0.006018 0.057352 0.036244 0.007852 0.057352 0.033485 0.021722 0.057352 0.025629 0.033481 0.057352 0.000000 0.045102 0.057352 0.014255 0.042267 0.057352 0.026340 0.034192 0.057352 0.034415 0.022107 0.057352 0.037250 0.007852 0.057352 0.034415 -0.006403 0.057352 0.026340 -0.018487 0.057352 0.014255 -0.026562 0.057352 0.000000 -0.029398 0.057352 -0.014255 -0.026562 0.057352 -0.026340 -0.018487 0.057352 -0.034415 -0.006403 0.057352 -0.037250 0.007852 0.057352 -0.034415 0.022107 0.057352 -0.026340 0.034192 0.057352 -0.014255 0.042267 0.057352 -0.000000 0.035352 -0.072648 0.010524 0.033259 -0.072648 0.019445 0.027298 -0.072648 0.025407 0.018376 -0.072648 0.027500 0.007852 -0.072648 0.025407 -0.002671 -0.072648 0.019445 -0.011593 -0.072648 0.010524 -0.017554 -0.072648 0.000000 -0.019648 -0.072648 -0.010524 -0.017554 -0.072648 -0.019445 -0.011593 -0.072648 -0.025407 -0.002671 -0.072648 -0.027500 0.007852 -0.072648 -0.025407 0.018376 -0.072648 -0.019445 0.027298 -0.072648 -0.010524 0.033259 -0.072648 0.013870 0.041338 0.057352 0.000000 0.044097 0.057352 -0.013870 0.041338 0.057352 -0.025629 0.033481 0.057352 -0.033485 0.021722 0.057352 -0.036244 0.007852 0.057352 -0.033485 -0.006018 0.057352 -0.025629 -0.017776 0.057352 -0.013870 -0.025633 0.057352 0.000000 -0.028392 0.057352 0.013870 -0.025633 0.057352 0.025629 -0.017776 0.057352 0.033485 -0.006018 0.057352 0.036244 0.007852 0.057352 0.033485 0.021722 0.057352 0.025629 0.033481 0.057352 0.000000 0.043069 0.057505 0.013477 0.040388 0.057505 0.024902 0.032754 0.057505 0.032536 0.021329 0.057505 0.035217 0.007852 0.057505 0.032536 -0.005625 0.057505 0.024902 -0.017050 0.057505 0.013477 -0.024684 0.057505 0.000000 -0.027364 0.057505 -0.013477 -0.024684 0.057505 -0.024902 -0.017050 0.057505 -0.032536 -0.005625 0.057505 -0.035217 0.007852 0.057505 -0.032536 0.021329 0.057505 -0.024902 0.032754 0.057505 -0.013477 0.040388 0.057505 -0.000000 0.033319 -0.072495 0.009746 0.031380 -0.072495 0.018008 0.025860 -0.072495 0.023528 0.017598 -0.072495 0.025467 0.007852 -0.072495 0.023528 -0.001893 -0.072495 0.018008 -0.010155 -0.072495 0.009746 -0.015676 -0.072495 0.000000 -0.017614 -0.072495 -0.009746 -0.015676 -0.072495 -0.018008 -0.010155 -0.072495 -0.023528 -0.001893 -0.072495 -0.025467 0.007852 -0.072495 -0.023528 0.017598 -0.072495 -0.018008 0.025860 -0.072495 -0.009746 0.031380 -0.072495 0.010524 0.033259 -0.069124 0.000000 0.035352 -0.069124 -0.010524 0.033259 -0.069124 -0.019445 0.027298 -0.069124 -0.025407 0.018376 -0.069124 -0.027500 0.007852 -0.069124 -0.025407 -0.002671 -0.069124 -0.019445 -0.011593 -0.069124 -0.010524 -0.017554 -0.069124 0.000000 -0.019648 -0.069124 0.010524 -0.017554 -0.069124 0.019445 -0.011593 -0.069124 0.025407 -0.002671 -0.069124 0.027500 0.007852 -0.069124 0.025407 0.018376 -0.069124 0.019445 0.027298 -0.069124</float_array><technique_common><accessor count="144" source="#Cylinder2-0-Pos-array" stride="3"><param name="X" type="float"/><param name="Y" type="float"/><param name="Z" type="float"/></accessor></technique_common></source><source id="Cylinder2-0-Normal"><float_array id="Cylinder2-0-Normal-array" count="390">-0.000000 0.000000 -1.000000 -0.194565 0.978142 -0.073362 -0.554073 0.829229 -0.073362 -0.829229 0.554073 -0.073362 -0.978142 0.194565 -0.073362 -0.978142 -0.194565 -0.073362 -0.829229 -0.554073 -0.073362 -0.554073 -0.829229 -0.073362 -0.194565 -0.978142 -0.073362 0.194565 -0.978142 -0.073362 0.554073 -0.829229 -0.073362 0.829229 -0.554073 -0.073362 0.978142 -0.194565 -0.073362 0.978142 0.194565 -0.073362 0.829229 0.554073 -0.073362 0.554073 0.829229 -0.073362 0.194565 0.978142 -0.073362 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.000000 0.000000 1.000000 0.194565 -0.978142 0.073362 0.554073 -0.829229 0.073362 0.829229 -0.554073 0.073362 0.978142 -0.194565 0.073362 0.978142 0.194565 0.073362 0.829229 0.554073 0.073362 0.554073 0.829229 0.073362 0.194565 0.978142 0.073362 -0.194565 0.978142 0.073362 -0.554073 0.829229 0.073362 -0.829229 0.554073 0.073362 -0.978142 0.194565 0.073362 -0.978142 -0.194565 0.073362 -0.829229 -0.554073 0.073362 -0.554073 -0.829229 0.073362 -0.194565 -0.978142 0.073362 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 -1.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 -0.014875 -0.074783 0.997089 0.014875 -0.074783 0.997089 -0.042361 0.063398 -0.997089 0.042361 -0.063398 0.997089 0.014875 0.074783 -0.997089 0.063398 0.042361 0.997089 -0.063398 0.042361 -0.997089 -0.042361 -0.063398 0.997089 -0.042361 0.063398 0.997089 -0.063398 0.042361 0.997089 0.063398 -0.042361 0.997089 -0.063398 -0.042361 0.997089 0.042361 0.063398 -0.997089 0.014875 0.074783 0.997089 0.042361 -0.063398 -0.997089 -0.074783 -0.014875 -0.997089 -0.074783 -0.014875 0.997089 0.074783 -0.014875 0.997089 0.074783 -0.014875 -0.997089 -0.063398 -0.042361 -0.997089 0.074783 0.014875 0.997089 0.063398 0.042361 -0.997089 -0.014875 0.074783 0.997089 -0.074783 0.014875 -0.997089 -0.074783 0.014875 0.997089 -0.014875 0.074783 -0.997089 0.074783 0.014875 -0.997089 0.042361 0.063398 0.997089 0.063398 -0.042361 -0.997089 0.014875 -0.074783 -0.997089 -0.014875 -0.074783 -0.997089 -0.042361 -0.063398 -0.997089 0.195090 0.980785 -0.000000 -0.195090 0.980785 0.000000 -0.555570 0.831470 0.000000 -0.831470 0.555570 0.000000 -0.980785 0.195090 0.000000 -0.980785 -0.195090 -0.000000 -0.831470 -0.555570 -0.000000 -0.555570 -0.831470 0.000000 -0.195090 -0.980785 0.000000 0.195090 -0.980785 -0.000000 0.555570 -0.831470 0.000000 0.831470 -0.555570 -0.000000 0.980785 -0.195090 -0.000000 0.980785 0.195090 0.000000 0.831470 0.555570 0.000000 0.555570 0.831470 0.000000</float_array><technique_common><accessor count="130" source="#Cylinder2-0-Normal-array" stride="3"><param name="X" type="float"/><param name="Y" type="float"/><param name="Z" type="float"/></accessor></technique_common></source><source id="Cylinder2-0-UV"><float_array id="Cylinder2-0-UV-array" count="2">0.000000 0.000000</float_array><technique_common><accessor count="1" source="#Cylinder2-0-UV-array" stride="2"><param name="S" type="float"/><param name="T" type="float"/></accessor></technique_common></source><vertices id="Cylinder2-0-Vtx"><input semantic="POSITION" source="#Cylinder2-0-Pos"/></vertices><polylist count="130" material="default"><input offset="0" semantic="VERTEX" source="#Cylinder2-0-Vtx"/><input offset="1" semantic="NORMAL" source="#Cylinder2-0-Normal"/><input offset="2" semantic="TEXCOORD" source="#Cylinder2-0-UV"/><vcount>4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 16 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 16</vcount><p>65 0 0 128 0 0 143 0 0 66 0 0 66 0 0 143 0 0 142 0 0 67 0 0 67 0 0 142 0 0 141 0 0 68 0 0 68 0 0 141 0 0 140 0 0 69 0 0 69 0 0 140 0 0 139 0 0 70 0 0 70 0 0 139 0 0 138 0 0 71 0 0 71 0 0 138 0 0 137 0 0 72 0 0 72 0 0 137 0 0 136 0 0 73 0 0 73 0 0 136 0 0 135 0 0 74 0 0 74 0 0 135 0 0 134 0 0 75 0 0 75 0 0 134 0 0 133 0 0 76 0 0 76 0 0 133 0 0 132 0 0 77 0 0 77 0 0 132 0 0 131 0 0 78 0 0 78 0 0 131 0 0 130 0 0 79 0 0 79 0 0 130 0 0 129 0 0 64 0 0 64 0 0 129 0 0 128 0 0 65 0 0 65 0 0 66 0 0 114 0 0 113 0 0 64 0 0 65 0 0 113 0 0 112 0 0 112 0 0 127 0 0 79 0 0 64 0 0 77 0 0 78 0 0 126 0 0 125 0 0 49 0 0 97 0 0 98 0 0 50 0 0 75 0 0 76 0 0 124 0 0 123 0 0 71 0 0 72 0 0 120 0 0 119 0 0 60 0 0 108 0 0 109 0 0 61 0 0 68 0 0 69 0 0 117 0 0 116 0 0 63 0 0 111 0 0 96 0 0 48 0 0 74 0 0 75 0 0 123 0 0 122 0 0 51 0 0 99 0 0 100 0 0 52 0 0 66 0 0 67 0 0 115 0 0 114 0 0 76 0 0 77 0 0 125 0 0 124 0 0 52 0 0 100 0 0 101 0 0 53 0 0 59 0 0 107 0 0 108 0 0 60 0 0 67 0 0 68 0 0 116 0 0 115 0 0 78 0 0 79 0 0 127 0 0 126 0 0 48 0 0 96 0 0 97 0 0 49 0 0 73 0 0 74 0 0 122 0 0 121 0 0 58 0 0 106 0 0 107 0 0 59 0 0 53 0 0 101 0 0 102 0 0 54 0 0 61 0 0 109 0 0 110 0 0 62 0 0 62 0 0 110 0 0 111 0 0 63 0 0 57 0 0 105 0 0 106 0 0 58 0 0 69 0 0 70 0 0 118 0 0 117 0 0 50 0 0 98 0 0 99 0 0 51 0 0 72 0 0 73 0 0 121 0 0 120 0 0 54 0 0 102 0 0 103 0 0 55 0 0 70 0 0 71 0 0 119 0 0 118 0 0 55 0 0 103 0 0 104 0 0 56 0 0 56 0 0 104 0 0 105 0 0 57 0 0 34 0 0 82 0 0 81 0 0 33 0 0 35 0 0 83 0 0 82 0 0 34 0 0 36 0 0 84 0 0 83 0 0 35 0 0 37 0 0 85 0 0 84 0 0 36 0 0 38 0 0 86 0 0 85 0 0 37 0 0 39 0 0 87 0 0 86 0 0 38 0 0 40 0 0 88 0 0 87 0 0 39 0 0 41 0 0 89 0 0 88 0 0 40 0 0 42 0 0 90 0 0 89 0 0 41 0 0 43 0 0 91 0 0 90 0 0 42 0 0 44 0 0 92 0 0 91 0 0 43 0 0 45 0 0 93 0 0 92 0 0 44 0 0 46 0 0 94 0 0 93 0 0 45 0 0 47 0 0 95 0 0 94 0 0 46 0 0 32 0 0 80 0 0 95 0 0 47 0 0 33 0 0 81 0 0 80 0 0 32 0 0 50 0 0 95 0 0 80 0 0 49 0 0 51 0 0 94 0 0 95 0 0 50 0 0 52 0 0 93 0 0 94 0 0 51 0 0 53 0 0 92 0 0 93 0 0 52 0 0 54 0 0 91 0 0 92 0 0 53 0 0 55 0 0 90 0 0 91 0 0 54 0 0 56 0 0 89 0 0 90 0 0 55 0 0 57 0 0 88 0 0 89 0 0 56 0 0 58 0 0 87 0 0 88 0 0 57 0 0 59 0 0 86 0 0 87 0 0 58 0 0 60 0 0 85 0 0 86 0 0 59 0 0 61 0 0 84 0 0 85 0 0 60 0 0 62 0 0 83 0 0 84 0 0 61 0 0 63 0 0 82 0 0 83 0 0 62 0 0 48 0 0 81 0 0 82 0 0 63 0 0 49 0 0 80 0 0 81 0 0 48 0 0 112 0 0 113 0 0 97 0 0 96 0 0 113 0 0 114 0 0 98 0 0 97 0 0 114 0 0 115 0 0 99 0 0 98 0 0 115 0 0 116 0 0 100 0 0 99 0 0 116 0 0 117 0 0 101 0 0 100 0 0 117 0 0 118 0 0 102 0 0 101 0 0 118 0 0 119 0 0 103 0 0 102 0 0 119 0 0 120 0 0 104 0 0 103 0 0 120 0 0 121 0 0 105 0 0 104 0 0 121 0 0 122 0 0 106 0 0 105 0 0 122 0 0 123 0 0 107 0 0 106 0 0 123 0 0 124 0 0 108 0 0 107 0 0 124 0 0 125 0 0 109 0 0 108 0 0 125 0 0 126 0 0 110 0 0 109 0 0 126 0 0 127 0 0 111 0 0 110 0 0 96 0 0 111 0 0 127 0 0 112 0 0 129 0 0 130 0 0 131 0 0 132 0 0 133 0 0 134 0 0 135 0 0 136 0 0 137 0 0 138 0 0 139 0 0 140 0 0 141 0 0 142 0 0 143 0 0 128 0 0 1 0 0 32 0 0 47 0 0 2 0 0 2 0 0 47 0 0 46 0 0 3 0 0 3 0 0 46 0 0 45 0 0 4 0 0 4 0 0 45 0 0 44 0 0 5 0 0 5 0 0 44 0 0 43 0 0 6 0 0 6 0 0 43 0 0 42 0 0 7 0 0 7 0 0 42 0 0 41 0 0 8 0 0 8 0 0 41 0 0 40 0 0 9 0 0 9 0 0 40 0 0 39 0 0 10 0 0 10 0 0 39 0 0 38 0 0 11 0 0 11 0 0 38 0 0 37 0 0 12 0 0 12 0 0 37 0 0 36 0 0 13 0 0 13 0 0 36 0 0 35 0 0 14 0 0 14 0 0 35 0 0 34 0 0 15 0 0 15 0 0 34 0 0 33 0 0 0 0 0 0 0 0 33 0 0 32 0 0 1 0 0 1 0 0 17 0 0 16 0 0 0 0 0 2 0 0 18 0 0 17 0 0 1 0 0 3 0 0 19 0 0 18 0 0 2 0 0 4 0 0 20 0 0 19 0 0 3 0 0 5 0 0 21 0 0 20 0 0 4 0 0 6 0 0 22 0 0 21 0 0 5 0 0 7 0 0 23 0 0 22 0 0 6 0 0 8 0 0 24 0 0 23 0 0 7 0 0 9 0 0 25 0 0 24 0 0 8 0 0 10 0 0 26 0 0 25 0 0 9 0 0 11 0 0 27 0 0 26 0 0 10 0 0 12 0 0 28 0 0 27 0 0 11 0 0 13 0 0 29 0 0 28 0 0 12 0 0 14 0 0 30 0 0 29 0 0 13 0 0 15 0 0 31 0 0 30 0 0 14 0 0 0 0 0 16 0 0 31 0 0 15 0 0 17 0 0 18 0 0 19 0 0 20 0 0 21 0 0 22 0 0 23 0 0 24 0 0 25 0 0 26 0 0 27 0 0 28 0 0 29 0 0 30 0 0 31 0 0 16 0 0</p></polylist></mesh></geometry></library_geometries>
<library_visual_scenes><visual_scene id="Scene" name="Scene"><node layer="L1" id="Cylinder2-0L1" name="Cylinder2-0"><translate sid="translate">0.00000 0.00000 0.00000</translate>
<rotate sid="rotateZ">0 0 1 0.00000</rotate><rotate sid="rotateY">0 1 0 0.00000</rotate><rotate sid="rotateX">1 0 0 0.00000</rotate><scale sid="scale">1.0000 1.0000 1.0000</scale><instance_geometry url="#Cylinder2-0"><bind_material><technique_common><instance_material symbol="default" target="#default"><bind_vertex_input input_semantic="TEXCOORD" input_set="1" semantic="CHANNEL1"/></instance_material></technique_common></bind_material></instance_geometry></node></visual_scene></library_visual_scenes>
<scene><instance_visual_scene url="#Scene"/></scene>
</COLLADA>