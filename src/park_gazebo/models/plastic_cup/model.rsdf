<?xml version="1.0" ?>
<%
  # SI units (length in meters)

  # Geometry
  # Height
  h  = 0.13
  # Base diameter
  d1 = 0.055
  b = d1/2.0
  # Top diameter
  d2 = 0.075
  a = d2/2.0

  # Inertia
  # http://www.wolframalpha.com/input/?i=conical+frustum+moment+of+inertia+tensor+of+solid

  #Need to calculate moment if bottom of frustum is closed
  mass = 0.0599
  ixx  = ( 2.0*h**2 * (a**2 + 3*a*b + 6*b**2) + 3*(a**4 + a**3*b + a**2*b**2 + a*b**3 + b**4) ) / ( 20.0*(a**2 + a*b + b**2) )*mass
  iyy  = ixx
  izz  = 3*(a**4 + a**3*b + a**2*b**2 + a*b**3 + b**4)/( 10*(a**2 + a*b + b**2 ) )*mass

%>
<sdf version="1.5">
  <model name="plastic_cup">
    <link name="link">
      <pose>0 0 <%= h/2 %> 0 0 0</pose>
      <inertial>
        <mass><%= mass %></mass>
        <inertia>
          <ixx><%= ixx %></ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy><%= iyy %></iyy>
          <iyz>0</iyz>
          <izz><%= izz %></izz>
        </inertia>
      </inertial>

      <collision name="collision">
        <geometry>
          <mesh>
            <uri>model://plastic_cup/meshes/plastic_cup.dae</uri>
          </mesh>
        </geometry>
        <surface>
          <contact>
            <ode>
              <max_vel>0.1</max_vel>
              <min_depth>0.001</min_depth>
            </ode>
          </contact>
        </surface>
      </collision>

      <visual name="visual">
        <geometry>
          <mesh>
            <uri>model://plastic_cup/meshes/plastic_cup.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/GreyTransparent</name>
          </script>
        </material>
      </visual>

    </link>
  </model>
</sdf>
