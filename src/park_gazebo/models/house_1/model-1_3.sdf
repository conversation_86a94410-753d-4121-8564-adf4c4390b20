<?xml version="1.0"?>
<sdf version="1.3">
  <model name="House 1">
    <static>true</static>
    <link name="link">
      <collision name="collision">
        <geometry>
          <mesh>
            <uri>model://house_1/meshes/house_1.dae</uri>
          </mesh>
        </geometry>
      </collision>
      <visual name="visual">
        <geometry>
          <mesh>
            <uri>model://house_1/meshes/house_1.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>model://house_1/materials/scripts</uri>
            <uri>model://house_1/materials/textures</uri>
            <name>House_1/Diffuse</name>
          </script>
          <shader type="normal_map_object_space">
            <normal_map>House_1_Normal.png</normal_map>
          </shader>
        </material>
      </visual>
    </link>
  </model>
</sdf>
