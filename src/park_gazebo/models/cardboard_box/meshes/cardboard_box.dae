<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>ColeB</author>
      <authoring_tool>OpenCOLLADA for 3ds Max;  Version: 1.6;  Revision: 24</authoring_tool>
    </contributor>
    <created>2016-07-25T18:14:43</created>
    <modified>2016-07-25T18:14:43</modified>
    <unit name="millimeter" meter="0.001"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="CardboardBoxes">
      <profile_COMMON>
        <newparam sid="CardboardBoxes_png-surface">
          <surface type="2D">
            <init_from>CardboardBoxes_png</init_from>
          </surface>
        </newparam>
        <newparam sid="CardboardBoxes_png-sampler">
          <sampler2D>
            <source>CardboardBoxes_png-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <blinn>
            <emission>
              <color>0 0 0 1</color>
            </emission>
            <ambient>
              <color>0.588 0.588 0.588 1</color>
            </ambient>
            <diffuse>
              <texture texture="CardboardBoxes_png-sampler" texcoord="CHANNEL1"/>
            </diffuse>
            <specular>
              <color>0 0 0 1</color>
            </specular>
            <shininess>
              <float>10</float>
            </shininess>
            <reflective>
              <color>0 0 0 1</color>
            </reflective>
            <transparent opaque="A_ONE">
              <color>1 1 1 1</color>
            </transparent>
            <transparency>
              <float>1</float>
            </transparency>
          </blinn>
        </technique>
      </profile_COMMON>
      <extra>
        <technique profile="OpenCOLLADA3dsMax">
          <extended_shader>
            <opacity_type sid="opacity_type" type="int">0</opacity_type>
            <falloff_type sid="falloff_type" type="int">0</falloff_type>
            <falloff sid="falloff" type="float">0</falloff>
            <index_of_refraction sid="index_of_refraction" type="float">1.5</index_of_refraction>
            <wire_size sid="wire_size" type="float">1</wire_size>
            <wire_units sid="wire_units" type="int">0</wire_units>
            <apply_reflection_dimming sid="apply_reflection_dimming" type="bool">0</apply_reflection_dimming>
            <dim_level sid="dim_level" type="float">0</dim_level>
            <reflection_level sid="reflection_level" type="float">3</reflection_level>
          </extended_shader>
          <shader>
            <ambient_diffuse_texture_lock sid="ambient_diffuse_texture_lock" type="bool">1</ambient_diffuse_texture_lock>
            <ambient_diffuse_lock sid="ambient_diffuse_lock" type="bool">1</ambient_diffuse_lock>
            <diffuse_specular_lock sid="diffuse_specular_lock" type="bool">0</diffuse_specular_lock>
            <use_self_illum_color sid="use_self_illum_color" type="bool">0</use_self_illum_color>
            <self_illumination sid="self_illumination" type="float">0</self_illumination>
            <specular_level sid="specular_level" type="float">0</specular_level>
            <soften sid="soften" type="float">0.1</soften>
          </shader>
        </technique>
      </extra>
    </effect>
  </library_effects>
  <library_materials>
    <material id="CardboardBoxes-material" name="CardboardBoxes">
      <instance_effect url="#CardboardBoxes"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="geom-Conveyor_Box" name="Conveyor_Box">
      <mesh>
        <source id="geom-Conveyor_Box-positions">
          <float_array id="geom-Conveyor_Box-positions-array" count="54">-198.5183 -198.5183 0 -198.5183 0 0 -198.5183 -198.5183 397.0367 198.5183 -198.5183 0 0 0 0 0 -198.5183 0 198.5183 -198.5183 397.0367 0 -198.5183 397.0367 0 0 397.0367 -198.5183 198.5183 0 0 198.5183 0 -198.5183 198.5183 397.0367 0 198.5183 397.0367 -198.5183 0 397.0367 198.5183 198.5183 0 198.5183 0 0 198.5183 198.5183 397.0367 198.5183 0 397.0367</float_array>
          <technique_common>
            <accessor source="#geom-Conveyor_Box-positions-array" count="18" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="geom-Conveyor_Box-normals">
          <float_array id="geom-Conveyor_Box-normals-array" count="126">0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 1 0 0 1 0 0 1 0 0 1 0 -1 0 0 -1 0 0 -1 0 0 -1 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 0 0 -1 0 0 -1 0 0 1 0 0 1 0 -1 0 0 -1 0 1 0 0 1 0 0 1 0 0 1 0 0 0 0 -1 0 0 -1 0 0 1 0 0 1 0 1 0 0 1 0 0 1 0 0 1 0 -1 0 0 -1 0 0 0 0 -1 0 0 1 0 1 0 0 1 0 1 0 0 1 0 0</float_array>
          <technique_common>
            <accessor source="#geom-Conveyor_Box-normals-array" count="42" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="geom-Conveyor_Box-map1">
          <float_array id="geom-Conveyor_Box-map1-array" count="144">0.002000085 0.3197314 0 0.002000085 0.6009098 0 0.8710723 0.2831782 0 0.5898938 0.2831782 0 0.5643569 0.3197314 0 0.8455351 0.3197313 0 0.8455353 0.6009097 0 0.5643569 0.6009098 0 0.002 0.03855312 0 0.5643567 0.03855291 0 0.5898938 0.002000002 0 0.8710723 0.002000002 0 0.5898938 0.002000002 0 0.8710723 0.002000002 0 0.8710723 0.2831782 0 0.5898938 0.2831782 0 0.5643569 0.3197314 0 0.8455351 0.3197313 0 0.8455353 0.6009097 0 0.5643569 0.6009098 0 0.002000085 0.3197314 0 0.002 0.03855312 0 0.5643567 0.03855291 0 0.002000085 0.6009098 0 0.5898938 0.002000002 0 0.8710723 0.002000002 0 0.8710723 0.2831782 0 0.5898938 0.2831782 0 0.5643569 0.3197314 0 0.8455351 0.3197313 0 0.8455353 0.6009097 0 0.5643569 0.6009098 0 0.002000085 0.3197314 0 0.002 0.03855312 0 0.5643567 0.03855291 0 0.002000085 0.6009098 0 0.5898938 0.002000002 0 0.5898938 0.2831782 0 0.8710723 0.2831782 0 0.8710723 0.002000002 0 0.5643569 0.3197314 0 0.5643569 0.6009098 0 0.8455353 0.6009097 0 0.8455351 0.3197313 0 0.002000085 0.3197314 0 0.5643567 0.03855291 0 0.002 0.03855312 0 0.002000085 0.6009098 0</float_array>
          <technique_common>
            <accessor source="#geom-Conveyor_Box-map1-array" count="48" stride="3">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
              <param name="P" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="geom-Conveyor_Box-vertices">
          <input semantic="POSITION" source="#geom-Conveyor_Box-positions"/>
        </vertices>
        <polylist material="CardboardBoxes" count="16">
          <input semantic="VERTEX" source="#geom-Conveyor_Box-vertices" offset="0"/>
          <input semantic="NORMAL" source="#geom-Conveyor_Box-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#geom-Conveyor_Box-map1" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 </vcount>
          <p>0 0 10 1 1 11 4 2 2 5 3 3 2 4 4 7 5 5 8 6 6 13 7 7 0 8 0 5 9 8 7 10 9 2 11 4 1 12 1 0 13 0 2 14 4 13 15 7 3 16 12 5 3 15 4 2 14 15 17 13 6 18 16 17 19 19 8 6 18 7 5 17 3 20 20 6 21 16 7 10 22 5 9 21 15 22 23 17 23 19 6 24 16 3 25 20 9 26 24 10 27 27 4 2 26 1 1 25 11 28 28 13 7 31 8 6 30 12 29 29 9 30 32 11 31 28 12 32 34 10 33 33 1 12 35 13 15 31 11 34 28 9 35 32 14 36 36 15 17 39 4 2 38 10 27 37 16 37 40 12 29 43 8 6 42 17 19 41 14 38 44 10 33 46 12 32 45 16 39 40 15 22 47 14 40 44 16 41 40 17 23 41</p>
        </polylist>
      </mesh>
    </geometry>
  </library_geometries>
  <library_lights>
    <light id="EnvironmentAmbientLight" name="EnvironmentAmbientLight">
      <technique_common>
        <ambient>
          <color>0 0 0</color>
        </ambient>
      </technique_common>
    </light>
  </library_lights>
  <library_images>
    <image id="CardboardBoxes_png">
      <init_from>cardboard_box.png</init_from>
    </image>
  </library_images>
  <library_visual_scenes>
    <visual_scene id="MaxScene">
      <node name="EnvironmentAmbientLight">
        <instance_light url="#EnvironmentAmbientLight"/>
      </node>
      <node id="node-Conveyor_Box" name="Conveyor_Box">
        <instance_geometry url="#geom-Conveyor_Box">
          <bind_material>
            <technique_common>
              <instance_material symbol="CardboardBoxes" target="#CardboardBoxes-material">
                <bind_vertex_input semantic="CHANNEL1" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
        <extra>
          <technique profile="OpenCOLLADA">
            <cast_shadows sid="cast_shadows" type="bool">1</cast_shadows>
            <receive_shadows sid="receive_shadows" type="bool">1</receive_shadows>
            <primary_visibility sid="primary_visibility" type="int">1</primary_visibility>
            <secondary_visibility sid="secondary_visibility" type="int">1</secondary_visibility>
          </technique>
        </extra>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#MaxScene"/>
  </scene>
</COLLADA>
