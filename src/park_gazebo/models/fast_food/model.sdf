<?xml version="1.0" ?>
<sdf version="1.5">
  <model name="Fast Food">
    <static>true</static>
    <link name="link">
      <pose>0 0 3.15931296 0 0 0</pose>
      <collision name="collision">
        <geometry>
          <mesh>
            <scale>3 3 2</scale>
            <uri>model://fast_food/meshes/fast_food.dae</uri>
          </mesh>
        </geometry>
      </collision>
      <visual name="visual">
        <geometry>
          <mesh>
            <scale>3 3 2</scale>
            <uri>model://fast_food/meshes/fast_food.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>model://fast_food/materials/scripts</uri>
            <uri>model://fast_food/materials/textures</uri>
            <name>FastFood/Diffuse</name>
          </script>
          <shader type="normal_map_tangent_space">
            <normal_map>FastFood_Normal.png</normal_map>
          </shader>
        </material>
      </visual>
    </link>
  </model>
</sdf>
