<?xml version="1.0" ?>
<sdf version="1.5">
  <world name="inspection_world">
    <physics name="default_physics" default="true" type="ode">
      <max_step_size>0.002</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>500</real_time_update_rate>
      <ode>
        <solver>
          <type>quick</type>
          <iters>50</iters>
          <sor>0.5</sor>
        </solver>
      </ode>
    </physics>
    <scene>
      <ambient>0.01 0.01 0.01 1.0</ambient>
      <sky>
        <clouds>
          <speed>12</speed>
        </clouds>
      </sky>
      <shadows>1</shadows>
    </scene>

    <!-- Global light sources -->
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 -0 0</pose>
      <diffuse>1.0 1.0 1.0 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 -0.5 -1.0</direction>
    </light>
    <light name='sun_diffuse' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 -10 0 -0 0</pose>
      <diffuse>1.0 1.0 1.0 1</diffuse>
      <specular>0.3 0.3 0.3 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>0.5 0.5 -1.0</direction>
    </light>

    <!-- The water level -->
    <!-- Disabled because UUV does not appear to be released for Noetic (yet)
    <include>
      <uri>model://water_table</uri>
      <pose>0 0 0 0 0 0</pose>
    </include>

    <plugin name="underwater_current_plugin" filename="libuuv_underwater_current_ros_plugin.so">
      <namespace>hydrodynamics</namespace>
      <constant_current>
        <topic>current_velocity</topic>
        <velocity>
          <mean>0</mean>
          <min>0</min>
          <max>5</max>
          <mu>0.0</mu>
          <noiseAmp>0.0</noiseAmp>
        </velocity>

        <horizontal_angle>
          <mean>0</mean>
          <min>-3.141592653589793238</min>
          <max>3.141592653589793238</max>
          <mu>0.0</mu>
          <noiseAmp>0.0</noiseAmp>
        </horizontal_angle>

        <vertical_angle>
          <mean>0</mean>
          <min>-3.141592653589793238</min>
          <max>3.141592653589793238</max>
          <mu>0.0</mu>
          <noiseAmp>0.0</noiseAmp>
        </vertical_angle>
      </constant_current>
    </plugin>

    <plugin name="sc_interface" filename="libuuv_sc_ros_interface_plugin.so"/>
    -->

    <!-- Origin placed in northern Alberta, Canada  -->
    <spherical_coordinates>
      <latitude_deg>57.0271155</latitude_deg>
      <longitude_deg>-115.426770</longitude_deg>
      <elevation>600</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>

    <!-- Point the camera so the robot's in-frame -->
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose>25.719423 -7.832449 7.892787 0.0 0.383643 -2.874995</pose>
        <view_controller>orbit</view_controller>
      </camera>
    </gui>
  </world>
</sdf>
