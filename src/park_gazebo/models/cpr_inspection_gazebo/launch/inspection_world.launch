<launch>
    <param name="inspection_geom" command="$(find xacro)/xacro --inorder '$(find cpr_inspection_gazebo)/urdf/inspection_geometry.urdf.xacro'" />

    <arg name="platform" default="$(optenv CPR_GAZEBO_PLATFORM husky)" />

    <arg name="robot_x" default="6.0"/>
    <arg name="robot_y" default="-18.0"/>
    <arg name="robot_z" default="2.0"/>
    <arg name="robot_yaw" default="0" />

    <arg name="world_x" default="0.0"/>
    <arg name="world_y" default="0.0"/>
    <arg name="world_z" default="0.0"/>
    <arg name="world_yaw" default="0.0"/>

    <arg name="use_sim_time" default="true" />
    <arg name="gui" default="true" />
    <arg name="headless" default="false" />
    <arg name="world_name" default="$(find cpr_inspection_gazebo)/worlds/inspection_world.world" />
    <include file="$(find gazebo_ros)/launch/empty_world.launch">
      <arg name="debug" value="0" />
      <arg name="gui" value="$(arg gui)" />
      <arg name="use_sim_time" value="$(arg use_sim_time)" />
      <arg name="headless" value="$(arg headless)" />
      <arg name="world_name" value="$(arg world_name)" />
    </include>

    <node name="inspection_world_spawner" pkg="gazebo_ros" type="spawn_model"
      args="-urdf -model inspection_geometry -param inspection_geom -x $(arg world_x) -y $(arg world_y) -z $(arg world_z) -Y $(arg world_yaw)" />

    <include file="$(find cpr_inspection_gazebo)/launch/spawn_$(arg platform).launch">
      <arg name="x" value="$(arg robot_x)"/>
      <arg name="y" value="$(arg robot_y)"/>
      <arg name="z" value="$(arg robot_z)"/>
      <arg name="yaw" value="$(arg robot_yaw)"/>
    </include>
</launch>
