<?xml version="1.0"?>
<package format="2">
  <name>cpr_inspection_gazebo</name>
  <version>0.2.8</version>
  <description>The cpr_inspection_gazebo package</description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <author email="d<PERSON><PERSON><PERSON>@clearpathrobotics.com"><PERSON></author>

  <license>BSD</license>

  <buildtool_depend>catkin</buildtool_depend>
  <exec_depend>cpr_accessories_gazebo</exec_depend>
  <exec_depend>gazebo_ros</exec_depend>
  <exec_depend>husky_gazebo</exec_depend>
  <exec_depend>jackal_gazebo</exec_depend>
  <exec_depend>warthog_gazebo</exec_depend>

  <!--
    UUV simulation does not appear to be released for Noetic; If it ever is
    we can re-enable this. But for now, just turn off the water
  <exec_depend>uuv_gazebo_worlds</exec_depend>

  <export>
    <gazebo_ros gazebo_media_path="${prefix}"
                gazebo_model_path="${prefix}/models"/>
  </export>
  -->
  <export></export>
</package>
