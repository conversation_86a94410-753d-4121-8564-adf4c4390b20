<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.82.7 commit date:2020-03-12, commit time:05:06, hash:375c7dc4caf4</authoring_tool>
    </contributor>
    <created>2020-06-19T13:00:45</created>
    <modified>2020-06-19T13:00:45</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="Water-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.1610908 0.2712543 0.2183633 1</color>
            </diffuse>
            <transparent opaque="A_ONE">
              <color sid="alpha">0 0 0 0.5</color>
            </transparent>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images/>
  <library_materials>
    <material id="Water-material" name="Water">
      <instance_effect url="#Water-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Plane_002-mesh" name="Plane.002">
      <mesh>
        <source id="Plane_002-mesh-positions">
          <float_array id="Plane_002-mesh-positions-array" count="12">-29.76167 -29.76167 0 29.76167 -29.76167 0 -29.76167 29.76167 0 29.76167 29.76167 0</float_array>
          <technique_common>
            <accessor source="#Plane_002-mesh-positions-array" count="4" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_002-mesh-normals">
          <float_array id="Plane_002-mesh-normals-array" count="3">0 0 1</float_array>
          <technique_common>
            <accessor source="#Plane_002-mesh-normals-array" count="1" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_002-mesh-map-0">
          <float_array id="Plane_002-mesh-map-0-array" count="12">1 0 0 1 0 0 1 0 1 1 0 1</float_array>
          <technique_common>
            <accessor source="#Plane_002-mesh-map-0-array" count="6" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Plane_002-mesh-vertices">
          <input semantic="POSITION" source="#Plane_002-mesh-positions"/>
        </vertices>
        <triangles material="Water-material" count="2">
          <input semantic="VERTEX" source="#Plane_002-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Plane_002-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Plane_002-mesh-map-0" offset="2" set="0"/>
          <p>1 0 0 2 0 1 0 0 2 1 0 3 3 0 4 2 0 5</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Plane_001" name="Plane.001" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Plane_002-mesh" name="Plane.001">
          <bind_material>
            <technique_common>
              <instance_material symbol="Water-material" target="#Water-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>