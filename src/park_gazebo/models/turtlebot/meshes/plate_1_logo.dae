<?xml version="1.0" encoding="utf-8"?>
<COLLADA version="1.4.0" xmlns="http://www.collada.org/2005/11/COLLADASchema">
	<asset>
		<contributor>
			<author>Illusoft Collada 1.4.0 plugin for Blender - http://colladablender.illusoft.com</author>
			<authoring_tool>Blender v:249 - Illusoft Collada Exporter v:0.3.162</authoring_tool>
			<comments></comments>
			<copyright></copyright>
			<source_data>file:///u/nkoenig/plate_1.blend</source_data>
		</contributor>
		<created>2011-03-08T12:40:21.485977</created>
		<modified>2011-03-08T12:40:21.485998</modified>
		<unit meter="1.0" name="centimeter"/>
		<up_axis>Z_UP</up_axis>
	</asset>
	<library_effects>
		<effect id="plate_1_tga-fx" name="plate_1_tga-fx">
			<profile_COMMON>
				<newparam sid="plate_1_tga-surface">
					<surface type="2D">
						<init_from>plate_1_tga-img</init_from>
						<format>A8R8G8B8</format>
					</surface>
				</newparam>
				<newparam sid="plate_1_tga-sampler">
					<sampler2D>
						<source>plate_1_tga-surface</source>
						<minfilter>LINEAR_MIPMAP_LINEAR</minfilter>
						<magfilter>LINEAR</magfilter>
					</sampler2D>
				</newparam>
				<technique sid="blender">
					<phong>
						<emission>
							<color>0.30000 0.30000 0.30000 1</color>
						</emission>
						<ambient>
							<color>1.00000 1.00000 1.00000 1</color>
						</ambient>
						<diffuse>
							<texture texcoord="CHANNEL1" texture="plate_1_tga-sampler"/>
						</diffuse>
						<specular>
							<color>0.56037 0.56037 0.56037 1</color>
						</specular>
						<shininess>
							<float>17.0</float>
						</shininess>
						<reflective>
							<color>1.00000 1.00000 1.00000 1</color>
						</reflective>
						<reflectivity>
							<float>0.0</float>
						</reflectivity>
						<transparent>
							<color>1 1 1 1</color>
						</transparent>
						<transparency>
							<float>0.0</float>
						</transparency>
					</phong>
				</technique>
			</profile_COMMON>
		</effect>
	</library_effects>
	<library_images>
		<image id="plate_1_tga-img" name="plate_1_tga-img">
			<init_from>../materials/textures/plate_1_logo.png</init_from>
		</image>
	</library_images>
	<library_materials>
		<material id="plate_1_tga" name="plate_1_tga">
			<instance_effect url="#plate_1_tga-fx"/>
		</material>
	</library_materials>
	<library_geometries>
		<geometry id="Cylinder_001" name="Cylinder_001">
			<mesh>
				<source id="Cylinder_001-Position">
					<float_array count="198" id="Cylinder_001-Position-array">113.64349 109.00957 -3.20050 132.72653 84.74424 -3.20050 146.70905 57.22226 -3.20050 155.05354 27.50126 -3.20050 157.43944 -3.27658 -3.20050 153.77504 -33.92852 -3.20050 144.20116 -63.27659 -3.20050 129.08569 -90.19299 -3.20050 109.00955 -113.64332 -3.20050 84.74422 -132.72638 -3.20050 57.22223 -146.70888 -3.20050 27.50121 -155.05338 -3.20050 -3.27666 -157.43929 -3.20050 -33.92861 -153.77486 -3.20050 -63.27670 -144.20096 -3.20050 -90.19308 -129.08545 -3.20050 -113.64341 -109.00932 -3.20050 -132.72646 -84.74397 -3.20050 -146.70891 -57.22197 -3.20050 -155.05342 -27.50094 -3.20050 -157.43929 3.27693 -3.20050 -153.77486 33.92887 -3.20050 -144.20091 63.27695 -3.20050 -129.08542 90.19334 -3.20050 -109.00925 113.64365 -3.20050 -84.74390 132.72667 -3.20050 -57.22189 146.70914 -3.20050 -27.50086 155.05362 -3.20050 3.27702 157.43947 -3.20050 33.92896 153.77501 -3.20050 63.27703 144.20107 -3.20050 90.19342 129.08554 -3.20050 113.64371 109.00935 3.20050 132.72672 84.74399 3.20050 146.70915 57.22196 3.20050 155.05360 27.50092 3.20050 157.43944 -3.27695 3.20050 153.77498 -33.92882 3.20050 144.20107 -63.27682 3.20050 129.08560 -90.19314 3.20050 109.00949 -113.64340 3.20050 84.74420 -132.72639 3.20050 57.22227 -146.70886 3.20050 27.50133 -155.05336 3.20050 -3.27647 -157.43929 3.20050 -33.92834 -153.77492 3.20050 -63.27638 -144.20110 3.20050 -90.19274 -129.08571 3.20050 -113.64307 -109.00967 3.20050 -132.72615 -84.74445 3.20050 -146.70868 -57.22257 3.20050 -155.05328 -27.50166 3.20050 -157.43929 3.27613 3.20050 -153.77504 33.92802 3.20050 -144.20129 63.27608 3.20050 -129.08598 90.19250 3.20050 -109.01005 113.64288 3.20050 -84.74489 132.72604 3.20050 -57.22305 146.70866 3.20050 -27.50216 155.05338 3.20050 3.27562 157.43950 3.20050 33.92752 153.77533 3.20050 63.27562 144.20168 3.20050 90.19209 129.08647 3.20050 0.00007 0.00009 -3.20050 0.00007 0.00009 3.20050</float_array>
					<technique_common>
						<accessor count="66" source="#Cylinder_001-Position-array" stride="3">
							<param type="float" name="X"></param>
							<param type="float" name="Y"></param>
							<param type="float" name="Z"></param>
						</accessor>
					</technique_common>
				</source>
				<source id="Cylinder_001-Normals">
					<float_array count="288" id="Cylinder_001-Normals-array">-0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 -0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 -0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 -0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 -0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 -0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 -0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 -0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 -0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 -0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 -0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 -0.00000 1.00000 0.00000 0.00000 -1.00000 -0.00000 -0.00000 1.00000 0.00000 -0.00000 -1.00000 -0.00000 0.00000 1.00000 0.00000 -0.00000 -1.00000 -0.00000 0.00000 1.00000 0.00000 -0.00000 -1.00000 -0.00000 0.00000 1.00000 0.00000 -0.00000 -1.00000 -0.00000 0.00000 1.00000 0.00000 -0.00000 -1.00000 -0.00000 0.00000 1.00000 0.00000 -0.00000 -1.00000 -0.00000 0.00000 1.00000 0.00000 -0.00000 -1.00000 -0.00000 0.00000 1.00000 0.00000 -0.00000 -1.00000 -0.00000 0.00000 1.00000 -0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 -0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 -0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 0.78604 0.61817 -0.00000 0.89154 0.45294 -0.00000 0.96277 0.27031 0.00000 0.99701 0.07729 -0.00000 0.99293 -0.11870 -0.00000 0.95069 -0.31013 -0.00000 0.87192 -0.48965 -0.00000 0.75964 -0.65034 -0.00000 0.61817 -0.78604 -0.00000 0.45294 -0.89154 -0.00000 0.27031 -0.96277 -0.00000 0.07729 -0.99701 -0.00000 -0.11870 -0.99293 -0.00000 -0.31013 -0.95069 -0.00000 -0.48965 -0.87192 -0.00000 -0.65034 -0.75964 -0.00000 -0.78604 -0.61817 -0.00000 -0.89154 -0.45295 -0.00000 -0.96277 -0.27031 -0.00000 -0.99701 -0.07729 0.00000 -0.99293 0.11870 0.00000 -0.95069 0.31013 -0.00000 -0.87192 0.48964 0.00000 -0.75964 0.65034 0.00000 -0.61817 0.78604 -0.00000 -0.45295 0.89154 0.00000 -0.27031 0.96277 0.00000 -0.07729 0.99701 0.00000 0.11870 0.99293 -0.00000 0.31013 0.95069 0.00000 0.48964 0.87192 0.00000 0.65034 0.75964 0.00001</float_array>
					<technique_common>
						<accessor count="96" source="#Cylinder_001-Normals-array" stride="3">
							<param type="float" name="X"></param>
							<param type="float" name="Y"></param>
							<param type="float" name="Z"></param>
						</accessor>
					</technique_common>
				</source>
				<source id="Cylinder_001-UV">
					<float_array count="768" id="Cylinder_001-UV-array">0.70376 0.70362 0.73229 0.99323 0.67524 0.99323 0.29419 0.29405 0.51914 0.10943 0.55084 0.15686 0.70376 0.70362 0.67524 0.99323 0.61929 0.98210 0.29419 0.29405 0.47881 0.06909 0.51914 0.10943 0.70376 0.70362 0.61929 0.98210 0.56658 0.96027 0.29419 0.29405 0.43137 0.03740 0.47881 0.06909 0.70376 0.70362 0.56658 0.96027 0.51915 0.92857 0.29419 0.29405 0.37867 0.01556 0.43137 0.03740 0.70376 0.70362 0.51915 0.92857 0.47881 0.88823 0.29419 0.29405 0.32271 0.00444 0.37867 0.01556 0.70376 0.70362 0.47881 0.88823 0.44711 0.84080 0.29419 0.29405 0.26567 0.00444 0.32271 0.00444 0.70376 0.70362 0.44711 0.84080 0.42528 0.78809 0.29419 0.29405 0.20971 0.01557 0.26567 0.00444 0.70376 0.70362 0.42528 0.78809 0.41415 0.73214 0.29419 0.29405 0.15701 0.03740 0.20971 0.01557 0.70376 0.70362 0.41415 0.73214 0.41415 0.67509 0.29419 0.29405 0.10957 0.06909 0.15701 0.03740 0.70376 0.70362 0.41415 0.67509 0.42528 0.61914 0.29419 0.29405 0.06923 0.10943 0.10957 0.06909 0.70376 0.70362 0.42528 0.61914 0.44711 0.56644 0.29419 0.29405 0.03754 0.15686 0.06923 0.10943 0.70376 0.70362 0.44711 0.56644 0.47881 0.51900 0.29419 0.29405 0.01571 0.20957 0.03754 0.15686 0.70376 0.70362 0.47881 0.51900 0.51915 0.47866 0.29419 0.29405 0.00458 0.26552 0.01571 0.20957 0.70376 0.70362 0.51915 0.47866 0.56658 0.44697 0.29419 0.29405 0.00458 0.32257 0.00458 0.26552 0.70376 0.70362 0.56658 0.44697 0.61929 0.42514 0.29419 0.29405 0.01571 0.37852 0.00458 0.32257 0.70376 0.70362 0.61929 0.42514 0.67524 0.41401 0.29419 0.29405 0.03754 0.43123 0.01571 0.37852 0.70376 0.70362 0.67524 0.41401 0.73229 0.41401 0.29419 0.29405 0.06923 0.47866 0.03754 0.43123 0.70376 0.70362 0.73229 0.41401 0.78824 0.42514 0.29419 0.29405 0.10957 0.51900 0.06923 0.47866 0.70376 0.70362 0.78824 0.42514 0.84094 0.44697 0.29419 0.29405 0.15701 0.55070 0.10957 0.51900 0.70376 0.70362 0.84094 0.44697 0.88838 0.47866 0.29419 0.29405 0.20971 0.57253 0.15701 0.55070 0.70376 0.70362 0.88838 0.47866 0.92872 0.51900 0.29419 0.29405 0.26567 0.58366 0.20971 0.57253 0.70376 0.70362 0.92872 0.51900 0.96041 0.56644 0.29419 0.29405 0.32271 0.58366 0.26567 0.58366 0.70376 0.70362 0.96041 0.56644 0.98224 0.61914 0.29419 0.29405 0.37867 0.57253 0.32271 0.58366 0.70376 0.70362 0.98224 0.61914 0.99337 0.67509 0.29419 0.29405 0.43137 0.55070 0.37867 0.57253 0.70376 0.70362 0.99337 0.67509 0.99337 0.73214 0.29419 0.29405 0.47881 0.51900 0.43137 0.55070 0.70376 0.70362 0.99337 0.73214 0.98224 0.78809 0.29419 0.29405 0.51914 0.47866 0.47881 0.51900 0.70376 0.70362 0.98224 0.78809 0.96041 0.84080 0.29419 0.29405 0.55084 0.43123 0.51914 0.47866 0.70376 0.70362 0.96041 0.84080 0.92872 0.88823 0.29419 0.29405 0.57267 0.37852 0.55084 0.43123 0.70376 0.70362 0.92872 0.88823 0.88838 0.92857 0.29419 0.29405 0.58380 0.32257 0.57267 0.37852 0.70376 0.70362 0.88838 0.92857 0.84094 0.96027 0.29419 0.29405 0.58380 0.26552 0.58380 0.32257 0.70376 0.70362 0.84094 0.96027 0.78824 0.98210 0.29419 0.29405 0.57267 0.20957 0.58380 0.26552 0.78824 0.98210 0.73229 0.99323 0.70376 0.70362 0.29419 0.29405 0.55084 0.15686 0.57267 0.20957 0.31084 0.98241 0.31084 0.99162 0.26642 0.99163 0.26642 0.99163 0.26642 0.98242 0.31084 0.98241 0.26642 0.98242 0.26642 0.99163 0.22201 0.99163 0.22201 0.99163 0.22201 0.98242 0.26642 0.98242 0.22201 0.98242 0.22201 0.99163 0.17760 0.99164 0.17760 0.99164 0.17760 0.98243 0.22201 0.98242 0.17760 0.98243 0.17760 0.99164 0.13319 0.99164 0.13319 0.99164 0.13319 0.98243 0.17760 0.98243 0.13319 0.98243 0.13319 0.99164 0.08879 0.99164 0.08879 0.99164 0.08879 0.98243 0.13319 0.98243 0.08879 0.98243 0.08879 0.99164 0.04440 0.99164 0.04440 0.99164 0.04440 0.98243 0.08879 0.98243 0.04440 0.98243 0.04440 0.99164 0.00000 0.99164 0.00000 0.99164 0.00000 0.98243 0.04440 0.98243 0.35527 0.97316 0.35527 0.98237 0.31087 0.98238 0.31087 0.98238 0.31086 0.97318 0.35527 0.97316 0.31086 0.97318 0.31087 0.98238 0.26646 0.98240 0.26646 0.98240 0.26646 0.97319 0.31086 0.97318 0.26646 0.97319 0.26646 0.98240 0.22206 0.98240 0.22206 0.98240 0.22206 0.97320 0.26646 0.97319 0.22206 0.97320 0.22206 0.98240 0.17765 0.98241 0.17765 0.98241 0.17765 0.97320 0.22206 0.97320 0.17765 0.97320 0.17765 0.98241 0.13324 0.98240 0.13324 0.98240 0.13324 0.97320 0.17765 0.97320 0.13324 0.97320 0.13324 0.98240 0.08883 0.98240 0.08883 0.98240 0.08883 0.97319 0.13324 0.97320 0.08883 0.97319 0.08883 0.98240 0.04441 0.98238 0.04441 0.98238 0.04442 0.97318 0.08883 0.97319 0.04442 0.97318 0.04441 0.98238 0.00000 0.98237 0.00000 0.98237 0.00000 0.97316 0.04442 0.97318 0.00000 0.96386 0.00000 0.95466 0.04439 0.95466 0.04439 0.95466 0.04439 0.96387 0.00000 0.96386 0.04439 0.96387 0.04439 0.95466 0.08879 0.95467 0.08879 0.95467 0.08878 0.96388 0.04439 0.96387 0.08878 0.96388 0.08879 0.95467 0.13318 0.95468 0.13318 0.95468 0.13318 0.96388 0.08878 0.96388 0.13318 0.96388 0.13318 0.95468 0.17757 0.95468 0.17757 0.95468 0.17757 0.96389 0.13318 0.96388 0.17757 0.96389 0.17757 0.95468 0.22196 0.95469 0.22196 0.95469 0.22196 0.96389 0.17757 0.96389 0.22196 0.96389 0.22196 0.95469 0.26636 0.95470 0.26636 0.95470 0.26636 0.96390 0.22196 0.96389 0.26636 0.96390 0.26636 0.95470 0.31075 0.95470 0.31075 0.95470 0.31075 0.96391 0.26636 0.96390 0.31075 0.96391 0.31075 0.95470 0.35515 0.95471 0.35515 0.95471 0.35515 0.96391 0.31075 0.96391 0.00000 0.97312 0.00000 0.96391 0.04439 0.96393 0.04439 0.96393 0.04439 0.97313 0.00000 0.97312 0.04439 0.97313 0.04439 0.96393 0.08878 0.96394 0.08878 0.96394 0.08878 0.97315 0.04439 0.97313 0.08878 0.97315 0.08878 0.96394 0.13317 0.96395 0.13317 0.96395 0.13317 0.97316 0.08878 0.97315 0.13317 0.97316 0.13317 0.96395 0.17756 0.96396 0.17756 0.96396 0.17756 0.97316 0.13317 0.97316 0.17756 0.97316 0.17756 0.96396 0.22195 0.96395 0.22195 0.96395 0.22196 0.97316 0.17756 0.97316 0.22196 0.97316 0.22195 0.96395 0.26635 0.96394 0.26635 0.96394 0.26635 0.97315 0.22196 0.97316 0.26635 0.97315 0.26635 0.96394 0.31074 0.96393 0.31074 0.96393 0.31075 0.97314 0.26635 0.97315 0.31075 0.97314 0.31074 0.96393 0.35514 0.96391 0.35514 0.96391 0.35515 0.97312 0.31075 0.97314 0.31084 0.99162 0.31084 0.98241 0.35526 0.98241 0.35526 0.98241 0.35527 0.99162 0.31084 0.99162</float_array>
					<technique_common>
						<accessor count="384" source="#Cylinder_001-UV-array" stride="2">
							<param type="float" name="S"></param>
							<param type="float" name="T"></param>
						</accessor>
					</technique_common>
				</source>
				<vertices id="Cylinder_001-Vertex">
					<input semantic="POSITION" source="#Cylinder_001-Position"/>
				</vertices>
				<triangles count="128" material="plate_1_tga">
					<input offset="0" semantic="VERTEX" source="#Cylinder_001-Vertex"/>
					<input offset="1" semantic="NORMAL" source="#Cylinder_001-Normals"/>
					<input offset="2" semantic="TEXCOORD" source="#Cylinder_001-UV"/>
					<p>64 0 0 0 0 1 1 0 2 65 1 3 33 1 4 32 1 5 64 2 6 1 2 7 2 2 8 65 3 9 34 3 10 33 3 11 64 4 12 2 4 13 3 4 14 65 5 15 35 5 16 34 5 17 64 6 18 3 6 19 4 6 20 65 7 21 36 7 22 35 7 23 64 8 24 4 8 25 5 8 26 65 9 27 37 9 28 36 9 29 64 10 30 5 10 31 6 10 32 65 11 33 38 11 34 37 11 35 64 12 36 6 12 37 7 12 38 65 13 39 39 13 40 38 13 41 64 14 42 7 14 43 8 14 44 65 15 45 40 15 46 39 15 47 64 16 48 8 16 49 9 16 50 65 17 51 41 17 52 40 17 53 64 18 54 9 18 55 10 18 56 65 19 57 42 19 58 41 19 59 64 20 60 10 20 61 11 20 62 65 21 63 43 21 64 42 21 65 64 22 66 11 22 67 12 22 68 65 23 69 44 23 70 43 23 71 64 24 72 12 24 73 13 24 74 65 25 75 45 25 76 44 25 77 64 26 78 13 26 79 14 26 80 65 27 81 46 27 82 45 27 83 64 28 84 14 28 85 15 28 86 65 29 87 47 29 88 46 29 89 64 30 90 15 30 91 16 30 92 65 31 93 48 31 94 47 31 95 64 32 96 16 32 97 17 32 98 65 33 99 49 33 100 48 33 101 64 34 102 17 34 103 18 34 104 65 35 105 50 35 106 49 35 107 64 36 108 18 36 109 19 36 110 65 37 111 51 37 112 50 37 113 64 38 114 19 38 115 20 38 116 65 39 117 52 39 118 51 39 119 64 40 120 20 40 121 21 40 122 65 41 123 53 41 124 52 41 125 64 42 126 21 42 127 22 42 128 65 43 129 54 43 130 53 43 131 64 44 132 22 44 133 23 44 134 65 45 135 55 45 136 54 45 137 64 46 138 23 46 139 24 46 140 65 47 141 56 47 142 55 47 143 64 48 144 24 48 145 25 48 146 65 49 147 57 49 148 56 49 149 64 50 150 25 50 151 26 50 152 65 51 153 58 51 154 57 51 155 64 52 156 26 52 157 27 52 158 65 53 159 59 53 160 58 53 161 64 54 162 27 54 163 28 54 164 65 55 165 60 55 166 59 55 167 64 56 168 28 56 169 29 56 170 65 57 171 61 57 172 60 57 173 64 58 174 29 58 175 30 58 176 65 59 177 62 59 178 61 59 179 64 60 180 30 60 181 31 60 182 65 61 183 63 61 184 62 61 185 31 62 186 0 62 187 64 62 188 65 63 189 32 63 190 63 63 191 0 64 192 32 64 193 33 64 194 33 64 195 1 64 196 0 64 197 1 65 198 33 65 199 34 65 200 34 65 201 2 65 202 1 65 203 2 66 204 34 66 205 35 66 206 35 66 207 3 66 208 2 66 209 3 67 210 35 67 211 36 67 212 36 67 213 4 67 214 3 67 215 4 68 216 36 68 217 37 68 218 37 68 219 5 68 220 4 68 221 5 69 222 37 69 223 38 69 224 38 69 225 6 69 226 5 69 227 6 70 228 38 70 229 39 70 230 39 70 231 7 70 232 6 70 233 7 71 234 39 71 235 40 71 236 40 71 237 8 71 238 7 71 239 8 72 240 40 72 241 41 72 242 41 72 243 9 72 244 8 72 245 9 73 246 41 73 247 42 73 248 42 73 249 10 73 250 9 73 251 10 74 252 42 74 253 43 74 254 43 74 255 11 74 256 10 74 257 11 75 258 43 75 259 44 75 260 44 75 261 12 75 262 11 75 263 12 76 264 44 76 265 45 76 266 45 76 267 13 76 268 12 76 269 13 77 270 45 77 271 46 77 272 46 77 273 14 77 274 13 77 275 14 78 276 46 78 277 47 78 278 47 78 279 15 78 280 14 78 281 15 79 282 47 79 283 48 79 284 48 79 285 16 79 286 15 79 287 16 80 288 48 80 289 49 80 290 49 80 291 17 80 292 16 80 293 17 81 294 49 81 295 50 81 296 50 81 297 18 81 298 17 81 299 18 82 300 50 82 301 51 82 302 51 82 303 19 82 304 18 82 305 19 83 306 51 83 307 52 83 308 52 83 309 20 83 310 19 83 311 20 84 312 52 84 313 53 84 314 53 84 315 21 84 316 20 84 317 21 85 318 53 85 319 54 85 320 54 85 321 22 85 322 21 85 323 22 86 324 54 86 325 55 86 326 55 86 327 23 86 328 22 86 329 23 87 330 55 87 331 56 87 332 56 87 333 24 87 334 23 87 335 24 88 336 56 88 337 57 88 338 57 88 339 25 88 340 24 88 341 25 89 342 57 89 343 58 89 344 58 89 345 26 89 346 25 89 347 26 90 348 58 90 349 59 90 350 59 90 351 27 90 352 26 90 353 27 91 354 59 91 355 60 91 356 60 91 357 28 91 358 27 91 359 28 92 360 60 92 361 61 92 362 61 92 363 29 92 364 28 92 365 29 93 366 61 93 367 62 93 368 62 93 369 30 93 370 29 93 371 30 94 372 62 94 373 63 94 374 63 94 375 31 94 376 30 94 377 32 95 378 0 95 379 31 95 380 31 95 381 63 95 382 32 95 383</p>
				</triangles>
			</mesh>
		</geometry>
	</library_geometries>
	<library_visual_scenes>
		<visual_scene id="good" name="good">
			<node layer="L1" id="Cylinder" name="Cylinder">
				<translate sid="translate">0.00000 0.00000 0.00000</translate>
				<rotate sid="rotateZ">0 0 1 -12.98355</rotate>
				<rotate sid="rotateY">0 1 0 -0.00000</rotate>
				<rotate sid="rotateX">1 0 0 0.00000</rotate>
				<scale sid="scale">0.00100 0.00100 0.00100</scale>
				<instance_geometry url="#Cylinder_001">
					<bind_material>
						<technique_common>
							<instance_material symbol="plate_1_tga" target="#plate_1_tga">
								<bind_vertex_input input_semantic="TEXCOORD" input_set="1" semantic="CHANNEL1"/>
							</instance_material>
						</technique_common>
					</bind_material>
				</instance_geometry>
			</node>
		</visual_scene>
	</library_visual_scenes>
	<library_physics_materials>
		<physics_material id="Cylinder-PhysicsMaterial" name="Cylinder-PhysicsMaterial">
			<technique_common>
				<dynamic_friction>0.5</dynamic_friction>
				<restitution>0.0</restitution>
				<static_friction>0.5</static_friction>
			</technique_common>
		</physics_material>
	</library_physics_materials>
	<library_physics_models>
		<physics_model id="good-PhysicsModel" name="good-PhysicsModel">
			<rigid_body name="Cylinder-RigidBody" sid="Cylinder-RigidBody">
				<technique_common>
					<dynamic>false</dynamic>
					<mass>0</mass>
					<instance_physics_material url="#Cylinder-PhysicsMaterial"/>
					<shape>
						<instance_geometry url="#Cylinder_001"/>
					</shape>
				</technique_common>
			</rigid_body>
		</physics_model>
	</library_physics_models>
	<library_physics_scenes>
		<physics_scene id="good-Physics" name="good-Physics">
			<instance_physics_model url="#good-PhysicsModel">
				<instance_rigid_body body="Cylinder-RigidBody" target="#Cylinder"/>
			</instance_physics_model>
		</physics_scene>
	</library_physics_scenes>
	<scene>
		<instance_physics_scene url="#good-Physics"/>
		<instance_visual_scene url="#good"/>
	</scene>
</COLLADA>
