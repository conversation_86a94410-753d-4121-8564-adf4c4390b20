<?xml version="1.0"?>
<launch>
    <!-- 基本参数配置 -->
    <arg name="use_sim_time" default="true"/>
    <arg name="gui" default="true"/>
    <arg name="debug" default="false"/>
    
    <!-- 设置Gazebo环境变量 -->
    <env name="GAZEBO_MODEL_PATH" value="$(find park_gazebo)/models:$(optenv GAZEBO_MODEL_PATH)"/>
    
    <!-- 启动Gazebo服务 -->
    <include file="$(find gazebo_ros)/launch/empty_world.launch">
        <!-- 指定要加载的世界文件 -->
        <arg name="world_name" value="$(find park_gazebo)/worlds/park_world.world"/>
        
        <arg name="debug" value="$(arg debug)" />
        <arg name="gui" value="$(arg gui)" />
        <arg name="paused" value="false"/>
        <arg name="use_sim_time" value="$(arg use_sim_time)"/>
        <arg name="headless" value="false"/>
    </include>
</launch>