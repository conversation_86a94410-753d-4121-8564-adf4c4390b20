<sdf version='1.7'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
      <spot>
        <inner_angle>0</inner_angle>
        <outer_angle>0</outer_angle>
        <falloff>0</falloff>
      </spot>
    </light>
    <gravity>0 0 -9.8</gravity>
    <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    <atmosphere type='adiabatic'/>
    <physics type='ode'>
      <max_step_size>0.001</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>1000</real_time_update_rate>
    </physics>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <wind/>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <model name='cafe'>
      <static>1</static>
      <link name='link'>
        <collision name='main_floor'>
          <pose>-0.4 -0.75 0.0948 0 -0 0</pose>
          <geometry>
            <box>
              <size>9.38 22.63 0.19</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='wall_1'>
          <pose>-5.03 0.53 1.415 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.12 23.16 2.83</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='wall_2'>
          <pose>4.24 -0.31 1.415 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.12 21.48 2.83</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='wall_3'>
          <pose>-1.48 7.43 1.415 0 -0 0</pose>
          <geometry>
            <box>
              <size>7.2 0.12 2.83</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='wall_4'>
          <pose>2.09 8.9 1.435 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.12 3.05 2.87</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='wall_5'>
          <pose>3.155 10.4 1.435 0 -0 0</pose>
          <geometry>
            <box>
              <size>2.13 0.12 2.87</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='wall_6'>
          <pose>0.615 -10.98 1.415 0 -0 0</pose>
          <geometry>
            <box>
              <size>7.36 0.15 2.83</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='wall_7'>
          <pose>-4.62 -10.98 1.415 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.93 0.15 2.83</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='wall_8'>
          <pose>-3.61 -10.98 2.69 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.09 0.15 0.28</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='coke'>
          <pose>-4.385 0.26 0.95 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.03 1.03 1.52</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://cafe/meshes/cafe.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>40.0282 2.70177 0 0 -0 0</pose>
    </model>
    <state world_name='default'>
      <sim_time>2702 839000000</sim_time>
      <real_time>575 280696056</real_time>
      <wall_time>1742020366 585187007</wall_time>
      <iterations>57291</iterations>
      <model name='Dumpster'>
        <pose>39.8912 -40.697 -0.823251 0.015401 -0.004604 -3.13161</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>39.8912 -40.697 -0.823251 0.015401 -0.004604 -3.13161</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 -0 -0 0 -0 0</acceleration>
          <wrench>-0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='ElectronicsRecycling'>
        <pose>12.2901 -32.299 -0.770454 0 -0 1.56616</pose>
        <scale>1 1 1</scale>
        <link name='body'>
          <pose>12.2901 -32.299 -0.770454 0 -0 1.56616</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Fast Food'>
        <pose>52.1669 -40.7056 -0.89963 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>52.1669 -40.7056 2.25968 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='House 1'>
        <pose>2.89356 -39.0044 -0.646209 0 0 -3.12634</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>2.89356 -39.0044 -0.646209 0 -0 -3.12634</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='House 2'>
        <pose>30.4368 -37.9796 -0.815687 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>30.4368 -37.9796 -0.815687 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='House 3'>
        <pose>49.8712 -20.3931 -0.858211 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>49.8712 -20.3931 -0.858211 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Kitchen Dining'>
        <pose>23.0155 21.2659 0 0 -0 0.698002</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>23.0155 21.2659 0 0 -0 0.698002</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='PEPSI_NEXT_CACRV'>
        <pose>15.8101 -26.2042 -0.836189 0.038259 0.000331 -0.035049</pose>
        <scale>1 1 1</scale>
        <link name='link_0'>
          <pose>15.8101 -26.2042 -0.836189 0.038259 0.000331 -0.035049</pose>
          <velocity>0.000157 0.000308 -0.002823 0.071189 -0.039676 -0.000363</velocity>
          <acceleration>-0.001322 0.00273 -0.043105 0.564769 0.319172 -1.3e-05</acceleration>
          <wrench>-0.001322 0.00273 -0.043105 0 -0 0</wrench>
        </link>
      </model>
      <model name='TrashBin'>
        <pose>14.2819 -29.9193 -0.857921 0 -0 1.59024</pose>
        <scale>1 1 1</scale>
        <link name='body'>
          <pose>14.2819 -29.9193 -0.857921 0 -0 1.59024</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='baylands_01'>
        <pose>63.1862 116.023 -1.3 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='body'>
          <pose>63.1862 116.023 -1.3 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='body2'>
          <pose>63.1862 116.023 -1.3 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='body3'>
          <pose>63.1862 116.023 -1.3 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='beer'>
        <pose>15.8788 -25.7933 -0.793108 2.38427 -1.46599 -0.996239</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>15.8577 -25.906 -0.80185 2.38427 -1.46599 -0.996239</pose>
          <velocity>0.368163 0.04568 0.230401 2.60088 -1.19467 2.17219</velocity>
          <acceleration>-0.19974 -2.25546 -8.046 -2.19506 1.19232 -2.33519</acceleration>
          <wrench>-0.077898 -0.879628 -3.13794 0 -0 0</wrench>
        </link>
      </model>
      <model name='beer_0'>
        <pose>649.324 387.441 -9.52765e+06 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>649.324 387.441 -9.52765e+06 0 -0 0</pose>
          <velocity>0 0 -12747.2 0 -0 0</velocity>
          <acceleration>0 0 -9.8 0 -0 0</acceleration>
          <wrench>0 0 -3.822 0 -0 0</wrench>
        </link>
      </model>
      <model name='beer_1'>
        <pose>13.8734 -29.2174 -0.949485 1.5771 -0.206348 1.53637</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>13.9883 -29.2212 -0.950194 1.5771 -0.206348 1.53637</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 -0 -0 0 -0 0</acceleration>
          <wrench>-0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='beer_clone'>
        <pose>-3.11695 -11.8537 -0.723893 -1.7e-05 -1.7e-05 -0.016182</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-3.11695 -11.8537 -0.608893 -1.7e-05 -1.7e-05 -0.016182</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 -9.8 0 -0 0</acceleration>
          <wrench>0 0 -3.822 0 -0 0</wrench>
        </link>
      </model>
      <model name='beer_clone_0'>
        <pose>-4.71837 25.0115 -0.904116 -7.5e-05 4.9e-05 -0.018387</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-4.71837 25.0115 -0.789116 -7.5e-05 4.9e-05 -0.018387</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0.003682 0.004432 19.5868 -0.032053 0.039752 -0.020874</acceleration>
          <wrench>0.001436 0.001729 7.63884 0 -0 0</wrench>
        </link>
      </model>
      <model name='bowl'>
        <pose>11.5576 -26.7978 -0.957892 0.166391 0.020288 0.019867</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>11.5576 -26.7978 -0.957892 0.166391 0.020288 0.019867</pose>
          <velocity>-0.000111 0.000798 -0.005195 0.151471 0.059695 0.006264</velocity>
          <acceleration>2.10049 -5.26519 -1.20141 -2.3197 -0.496605 -0.007975</acceleration>
          <wrench>0.210049 -0.526519 -0.120141 0 -0 0</wrench>
        </link>
      </model>
      <model name='cafe'>
        <pose>17.9659 -44.1827 -0.727864 0 -0 3.13332</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>17.9659 -44.1827 -0.727864 0 -0 3.13332</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='cardboard_box'>
        <pose>11.362 -28.9668 -0.76017 0.047326 0.013058 0.020404</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>11.362 -28.9668 -0.76017 0.047326 0.013058 0.020404</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 -0 -0 -1e-06 -0 0</acceleration>
          <wrench>-0 -1e-06 -1e-06 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can'>
        <pose>54.2536 -11.9812 -0.917424 -1.57546 -0.326732 -2.56344</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>54.2536 -11.9812 -0.917424 -1.57546 -0.326732 -2.56344</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0.174069 0.107793 -0.017025 -1.37838 -1.16718 -3.07638</acceleration>
          <wrench>0.067887 0.042039 -0.00664 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_0'>
        <pose>40.3337 -27.6778 -1.09655 -0.059282 0.006265 -0.01165</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>40.3337 -27.6778 -1.09655 -0.059282 0.006265 -0.01165</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.001212 -0.005977 -4.7e-05 0.104176 -0.021177 -0.002249</acceleration>
          <wrench>-0.000473 -0.002331 -1.8e-05 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_0_clone'>
        <pose>18.1946 -28.0774 -0.907533 -1.54562 0.763585 -0.143905</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>18.1946 -28.0774 -0.907533 -1.54562 0.763585 -0.143905</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-2e-06 0 -1e-06 -3.8e-05 -0.000237 -6e-06</acceleration>
          <wrench>-1e-06 0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_1'>
        <pose>23.9273 -28.3262 -0.925199 1.56186 -0.599718 -0.530474</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>23.9273 -28.3262 -0.925199 1.56186 -0.599718 -0.530474</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-6e-06 4e-06 -1e-06 -0.000362 -0.000554 5e-06</acceleration>
          <wrench>-2e-06 2e-06 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_10'>
        <pose>20.7334 2.28089 0.657862 1.54664 0.699323 0.891834</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>20.7334 2.28089 0.657862 1.54664 0.699323 0.891834</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-1.4e-05 -1.5e-05 0 0.0015 -0.001222 2.8e-05</acceleration>
          <wrench>-5e-06 -6e-06 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_11'>
        <pose>20.8892 0.220606 1.41204 -1.57558 -0.799373 2.45964</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>20.8892 0.220606 1.41204 -1.57558 -0.799373 2.45964</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>2e-06 -2e-06 -0 0.000153 0.000179 1e-06</acceleration>
          <wrench>1e-06 -1e-06 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_2'>
        <pose>-11.6052 11.4665 -0.103899 -0.007961 -0.003685 1.5e-05</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-11.6052 11.4665 -0.103899 -0.007961 -0.003685 1.5e-05</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 -0 -0 6e-06 -3e-06 0</acceleration>
          <wrench>-0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_3'>
        <pose>-13.7497 15.9585 0.010571 0.085774 -0.051606 0.006857</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-13.7497 15.9585 0.010571 0.085774 -0.051606 0.006857</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-3e-06 2e-05 -5e-06 -0.000355 -5.4e-05 4e-05</acceleration>
          <wrench>-1e-06 8e-06 -2e-06 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_4'>
        <pose>-11.283 8.18683 -0.10389 -0.007955 -0.003397 1.5e-05</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-11.283 8.18683 -0.10389 -0.007955 -0.003397 1.5e-05</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 -0 -0 6e-06 -3e-06 0</acceleration>
          <wrench>-0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_5'>
        <pose>-28.7389 6.56109 -0.642176 -1.63114 -0.231784 3.083</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-28.7389 6.56109 -0.642176 -1.63114 -0.231784 3.083</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>4e-06 -0 0 2.8e-05 0.000439 2e-05</acceleration>
          <wrench>2e-06 -0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_6'>
        <pose>10.6581 16.5288 -0.029886 -0.019415 0.001571 8e-05</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>10.6581 16.5288 -0.029886 -0.019415 0.001571 8e-05</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>3e-06 -2e-06 -1e-06 3.1e-05 6e-05 -1.5e-05</acceleration>
          <wrench>1e-06 -1e-06 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_7'>
        <pose>-15.0902 -7.42492 -0.690178 1.56449 -0.766897 -1.17658</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-15.0902 -7.42492 -0.690178 1.56449 -0.766897 -1.17658</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.006548 -0.001606 -0.00061 -0.01835 -0.218984 -0.000812</acceleration>
          <wrench>-0.002554 -0.000626 -0.000238 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_8'>
        <pose>14.0888 -7.45313 0.001222 0.011444 -0.01976 -0.000135</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>14.0888 -7.45313 0.001222 0.011444 -0.01976 -0.000135</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0.153592 -0.098846 0.004575 -1.3784 0.405383 3.05268</acceleration>
          <wrench>0.059901 -0.03855 0.001784 0 -0 0</wrench>
        </link>
      </model>
      <model name='coke_can_9'>
        <pose>12.2074 -1.10864 0.000258 -2.01409 1.08082 1.58188</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>12.2074 -1.10864 0.000258 -2.01409 1.08082 1.58188</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-4.2e-05 8.4e-05 2e-06 -0.008149 -0.003671 -0.001453</acceleration>
          <wrench>-1.6e-05 3.3e-05 1e-06 0 -0 0</wrench>
        </link>
      </model>
      <model name='first_2015_trash_can'>
        <pose>10.9355 -34.7763 -0.738204 -0.010768 0.003062 -1.56549</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>10.9355 -34.7763 -0.738204 -0.010768 0.003062 -1.56549</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0.369111 2.34774 -4.05595 -0.075706 1.01828 -0.192796</acceleration>
          <wrench>1.78309 11.3414 -19.5933 0 -0 0</wrench>
        </link>
      </model>
      <model name='master_chef_can'>
        <pose>12.1411 -26.7884 -0.813053 -0.048652 -0.00531 -2.95985</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>12.1421 -26.7917 -0.743137 -0.048652 -0.00531 -2.95985</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 -9.8 0 -0 0</acceleration>
          <wrench>0 0 -3.822 0 -0 0</wrench>
        </link>
      </model>
      <model name='master_chef_can_clone'>
        <pose>-12.0703 11.0477 -0.05072 1.57064 -1.0572 0.361041</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-12.0456 10.9822 -0.050714 1.57064 -1.0572 0.361041</pose>
          <velocity>0.213973 0.085105 -4.8e-05 1.43948 -1.13789 -3.13928</velocity>
          <acceleration>-0.018068 0.083095 -1.12808 -2.36088 0.72883 2.64183</acceleration>
          <wrench>-0.007047 0.032407 -0.439952 0 -0 0</wrench>
        </link>
      </model>
      <model name='movable_coke_can_green'>
        <pose>13.9524 -27.0343 -0.854321 0.034016 -0.033863 1.00202</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>13.9524 -27.0343 -0.854321 0.034016 -0.033863 1.00202</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 -9.8 0 -0 0</acceleration>
          <wrench>0 0 -0.14602 0 -0 0</wrench>
        </link>
      </model>
      <model name='movable_coke_can_green_0'>
        <pose>14.5808 -26.2374 -0.823511 0.047618 0.006361 0.085788</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>14.5808 -26.2374 -0.823511 0.047618 0.006361 0.085788</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 -9.8 0 -0 0</acceleration>
          <wrench>0 0 -0.14602 0 -0 0</wrench>
        </link>
      </model>
      <model name='person_standing'>
        <pose>21.1286 -32.5453 -0.538064 0 1e-06 2.99159</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>21.1286 -32.5453 -0.538064 0 1e-06 2.99159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>1.93736 -1.7511 1.40834 -1.1679 1.14289 -3.01607</acceleration>
          <wrench>154.989 -140.088 112.667 0 -0 0</wrench>
        </link>
      </model>
      <model name='person_walking'>
        <pose>6.164 -33.1244 -0.688843 -0.017106 0.012039 0.114299</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>6.164 -33.1244 -0.688843 -0.017106 0.012039 0.114299</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 -9.8 -0 -0 -0</acceleration>
          <wrench>0 0 -784 0 -0 0</wrench>
        </link>
      </model>
      <model name='plastic_cup'>
        <pose>12.7339 -25.6841 -260.376 1.53555 -0.225966 0.941554</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>12.7861 -25.7227 -260.374 1.53555 -0.225966 0.941554</pose>
          <velocity>-1e-06 -1e-06 -71.3244 2.6e-05 -3.2e-05 -2e-06</velocity>
          <acceleration>0 0 -9.8 0 -0 0</acceleration>
          <wrench>0 0 -0.58702 0 -0 0</wrench>
        </link>
      </model>
      <model name='playground'>
        <pose>21.9056 -2.8802 0 0 -0 1.72306</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>21.9056 -2.8802 0 0 -0 1.72306</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='soft_envelope'>
        <pose>15.2735 -27.0689 -0.801704 0.04687 0.01036 0.001209</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>15.2735 -27.0689 -0.801704 0.04687 0.01036 0.001209</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='turtle'>
        <pose>10.5402 -26.2341 -0.660149 0.240626 0.004765 -0.026115</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>10.5402 -26.2341 -0.660149 0.240626 0.004765 -0.026115</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.112029 4.46853 3.08441 -1.37788 0.970942 0.401215</acceleration>
          <wrench>-2.24059 89.3706 61.6882 0 -0 0</wrench>
        </link>
      </model>
      <light name='sun'>
        <pose>0 0 10 0 -0 0</pose>
      </light>
    </state>
    <model name='Fast Food'>
      <static>1</static>
      <link name='link'>
        <pose>0 0 3.15931 0 -0 0</pose>
        <collision name='collision'>
          <geometry>
            <mesh>
              <scale>3 3 2</scale>
              <uri>model://fast_food/meshes/fast_food.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <scale>3 3 2</scale>
              <uri>model://fast_food/meshes/fast_food.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://fast_food/materials/scripts</uri>
              <uri>model://fast_food/materials/textures</uri>
              <name>FastFood/Diffuse</name>
            </script>
            <shader type='normal_map_tangent_space'>
              <normal_map>FastFood_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>37.3166 -38.8814 0 0 -0 0</pose>
    </model>
    <model name='baylands_01'>
      <pose>-5.54539 19.0529 -1.3 0 -0 0</pose>
      <static>1</static>
      <link name='body'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://baylands/media/baylands_01.DAE</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://baylands/media/scripts/</uri>
              <uri>model://baylands/media/Textures/</uri>
              <name>OakTree/Branch</name>
            </script>
          </material>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://baylands/media/baylands_01.DAE</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='body2'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://baylands/media/baylands_02.DAE</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://baylands/media/scripts/</uri>
              <uri>model://baylands/media/Textures/</uri>
              <name>OakTree/Branch</name>
            </script>
          </material>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://baylands/media/baylands_02.DAE</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='body3'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://baylands/media/baylands_03.DAE</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://baylands/media/baylands_03.DAE</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='beer'>
      <link name='link'>
        <pose>0 0 0.115 0 -0 0</pose>
        <inertial>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00058</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00058</iyy>
            <iyz>0</iyz>
            <izz>0.00019</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.055</radius>
              <length>0.23</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.055</radius>
              <length>0.23</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>model://beer/materials/scripts</uri>
              <uri>model://beer/materials/textures</uri>
              <name>Beer/Diffuse</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>11.7927 -25.9347 0 0 -0 0</pose>
    </model>
    <model name='bowl'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.0175 0 -0 0</pose>
          <mass>0.1</mass>
          <inertia>
            <ixx>0.000250308</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.000250308</iyy>
            <iyz>0</iyz>
            <izz>0.0004802</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0 0 0.0175 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.098</radius>
              <length>0.035</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://bowl/meshes/bowl.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>11.5474 -26.7566 0 0 -0 0</pose>
    </model>
    <model name='beer_0'>
      <link name='link'>
        <pose>0 0 0.115 0 -0 0</pose>
        <inertial>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00058</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00058</iyy>
            <iyz>0</iyz>
            <izz>0.00019</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.055</radius>
              <length>0.23</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.055</radius>
              <length>0.23</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>model://beer/materials/scripts</uri>
              <uri>model://beer/materials/textures</uri>
              <name>Beer/Diffuse</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>20.0314 -23.1636 0 0 -0 0</pose>
    </model>
    <model name='coke_can'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>33.966 -18.4203 0 0 -0 0</pose>
    </model>
    <model name='coke_can_0'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>40.3338 -27.6779 0 0 -0 0</pose>
    </model>
    <model name='coke_can_1'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>23.9448 -28.3038 0 0 -0 0</pose>
    </model>
    <model name='Dumpster'>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <scale>1.5 1.5 1.5</scale>
              <uri>model://dumpster/meshes/dumpster.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <scale>1.5 1.5 1.5</scale>
              <uri>model://dumpster/meshes/dumpster.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://dumpster/materials/scripts</uri>
              <uri>model://dumpster/materials/textures</uri>
              <name>Dumpster/Diffuse</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <inertial>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>39.6181 -35.917 0 0 -0 0</pose>
    </model>
    <model name='first_2015_trash_can'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.3683 0 -0 0</pose>
          <mass>4.83076</mass>
          <inertia>
            <ixx>0.281534</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.281534</iyy>
            <iyz>0</iyz>
            <izz>0.126223</izz>
          </inertia>
        </inertial>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://first_2015_trash_can/meshes/trash_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://first_2015_trash_can/meshes/trash_can.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>24.0216 -27.9734 0 0 -0 0</pose>
    </model>
    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
              <scale>1.5 1.5 1.5</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
              <scale>1.5 1.5 1.5</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_tangent_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>29.8793 -36.0475 0 0 -0 0</pose>
    </model>
    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
              <scale>1.5 1.5 1.5</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
              <scale>1.5 1.5 1.5</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_tangent_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>3.07041 -39.0685 0 0 -0 0</pose>
    </model>
    <model name='House 3'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
              <scale>1.5 1.5 1.5</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
              <scale>1.5 1.5 1.5</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_3/materials/scripts</uri>
              <uri>model://house_3/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_3/Diffuse</name>
            </script>
            <shader type='normal_map_tangent_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>52.8186 -23.0043 0 0 -0 0</pose>
    </model>
    <model name='Kitchen Dining'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://kitchen_dining/meshes/kitchen_dining.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>27.8412 16.3641 0 0 -0 0</pose>
    </model>
    <model name='person_standing'>
      <link name='link'>
        <inertial>
          <pose>0 -0.1 0.95 0 -0 0</pose>
          <mass>80</mass>
          <inertia>
            <ixx>24.88</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>25.73</iyy>
            <iyz>0</iyz>
            <izz>2.48</izz>
          </inertia>
        </inertial>
        <collision name='bottom'>
          <pose>0 -0.1 0.01 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.5 0.35 0.02</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='person'>
          <pose>0 0 0.02 0.04 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://person_standing/meshes/standing.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <pose>0 0 0.02 0.04 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://person_standing/meshes/standing.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>26.1773 -27.4753 0 0 -0 0</pose>
    </model>
    <model name='person_walking'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.95 0 -0 0</pose>
          <mass>80</mass>
          <inertia>
            <ixx>27.82</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>24.88</iyy>
            <iyz>0</iyz>
            <izz>4.57</izz>
          </inertia>
        </inertial>
        <collision name='bottom'>
          <pose>0 0 0.01 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.35 0.75 0.02</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='person'>
          <pose>0 0 -0.02 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://person_walking/meshes/walking.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <pose>0 0 -0.02 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://person_walking/meshes/walking.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>6.87633 -30.6342 0 0 -0 0</pose>
    </model>
    <model name='plastic_cup'>
      <link name='link'>
        <pose>0 0 0.065 0 -0 0</pose>
        <inertial>
          <mass>0.0599</mass>
          <inertia>
            <ixx>0.000302896</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.000302896</iyy>
            <iyz>0</iyz>
            <izz>3.28764e-05</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://plastic_cup/meshes/plastic_cup.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <contact>
              <poissons_ratio>0.35</poissons_ratio>
              <elastic_modulus>3.10264e+09</elastic_modulus>
              <ode>
                <kp>100000</kp>
                <kd>100</kd>
                <max_vel>100</max_vel>
                <min_depth>0.001</min_depth>
              </ode>
            </contact>
            <friction>
              <torsional>
                <coefficient>1</coefficient>
                <use_patch_radius>0</use_patch_radius>
                <surface_radius>0.01</surface_radius>
                <ode/>
              </torsional>
              <ode/>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://plastic_cup/meshes/plastic_cup.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/GreyTransparent</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>12.6052 -25.6589 0 0 -0 0</pose>
    </model>
    <model name='playground'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://playground/meshes/playground.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://playground/meshes/playground.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>22.7087 -2.72929 0 0 -0 0</pose>
    </model>
    <model name='movable_coke_can_green'>
      <link name='link'>
        <inertial>
          <pose>0 0 0 0 -0 0</pose>
          <mass>0.0149</mass>
          <inertia>
            <ixx>1e-05</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1e-05</iyy>
            <iyz>0</iyz>
            <izz>1e-05</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0 0 0.061 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.033</radius>
              <length>0.122</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>https://fuel.gazebosim.org/1.0/myoan/models/movable green coke can/1/files/meshes/coke_can.obj</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>13.9211 -24.8726 0 0 -0 0</pose>
    </model>
    <model name='movable_coke_can_green_0'>
      <link name='link'>
        <inertial>
          <pose>0 0 0 0 -0 0</pose>
          <mass>0.0149</mass>
          <inertia>
            <ixx>1e-05</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1e-05</iyy>
            <iyz>0</iyz>
            <izz>1e-05</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0 0 0.061 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.033</radius>
              <length>0.122</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>https://fuel.gazebosim.org/1.0/myoan/models/movable green coke can/1/files/meshes/coke_can.obj</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>14.5813 -26.2139 0 0 -0 0</pose>
    </model>
    <model name='soft_envelope'>
      <pose>14.2538 -26.0851 0 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.0064333</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.0077083</iyy>
            <iyz>0</iyz>
            <izz>0.010875</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.27 0.25 0.14</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>0.3</mu>
                <mu2>0.3</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>30000</kp>
                <kd>20</kd>
                <min_depth>0.002</min_depth>
                <max_vel>0.05</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>https://fuel.gazebosim.org/1.0/paserv/models/soft envelope medium size/1/files/meshes/model.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose>63.404 19.8963 57.2211 0 0.693797 -2.51915</pose>
        <view_controller>orbit</view_controller>
        <projection_type>perspective</projection_type>
      </camera>
    </gui>
    <model name='turtle'>
      <link name='link'>
        <inertial>
          <pose>0 0 -0.3 0 -0 0</pose>
          <mass>20</mass>
          <inertia>
            <ixx>0.70066</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.70066</iyy>
            <iyz>0</iyz>
            <izz>1.225</izz>
          </inertia>
        </inertial>
        <collision name='turtle_collision'>
          <pose>0.08 0 0.05 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.35</radius>
              <length>0.23</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='turtle_visual'>
          <geometry>
            <mesh>
              <uri>/home/<USER>/.ignition/fuel/fuel.ignitionrobotics.org/openrobotics/models/turtle/5/meshes/turtle.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>10.5779 -26.0598 0 0 -0 0</pose>
    </model>
    <model name='beer_1'>
      <link name='link'>
        <pose>0 0 0.115 0 -0 0</pose>
        <inertial>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00058</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00058</iyy>
            <iyz>0</iyz>
            <izz>0.00019</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.055</radius>
              <length>0.23</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.055</radius>
              <length>0.23</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>https://fuel.ignitionrobotics.org/1.0/openrobotics/models/beer/3/files/materials/scripts</uri>
              <uri>https://fuel.ignitionrobotics.org/1.0/openrobotics/models/beer/3/files/materials/textures</uri>
              <name>Beer/Diffuse</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>13.5745 -27.7222 0 0 -0 0</pose>
    </model>
    <model name='cardboard_box'>
      <pose>10.7649 -28.6328 0.15 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>2</mass>
          <inertia>
            <ixx>0.0416667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.0566667</iyy>
            <iyz>0</iyz>
            <izz>0.0683333</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.5 0.4 0.3</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0 0 -0.15 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>https://fuel.ignitionrobotics.org/1.0/openrobotics/models/cardboard box/3/files/meshes/cardboard_box.dae</uri>
              <scale>1.25932 1.00745 0.755591</scale>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='master_chef_can'>
      <link name='link'>
        <pose>0 0 0.07 0 -0 0</pose>
        <inertial>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00058</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00058</iyy>
            <iyz>0</iyz>
            <izz>0.00019</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <pose>0 0 -0.005 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.05</radius>
              <length>0.14</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>https://fuel.ignitionrobotics.org/1.0/petermitrano/models/master chef can/2/files/textured.obj</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>10.1341 -25.7774 0 0 -0 0</pose>
    </model>
    <model name='coke_can_0_clone'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>20.4992 -28.6373 -1.09019 0.003527 -0.04939 -0.011819</pose>
    </model>
    <model name='ElectronicsRecycling'>
      <static>1</static>
      <link name='body'>
        <pose>0 0 0 0 -0 0</pose>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://ElectronicsRecycling/meshes/ElectronicsRecycling.obj</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://ElectronicsRecycling/meshes/ElectronicsRecycling.obj</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>12.3131 -32.0133 0 0 -0 0</pose>
      <enable_wind>0</enable_wind>
    </model>
    <model name='PEPSI_NEXT_CACRV'>
      <link name='link_0'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://PEPSI_NEXT_CACRV/meshes/model.obj</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='collision_0'>
          <geometry>
            <mesh>
              <uri>model://PEPSI_NEXT_CACRV/meshes/model.obj</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <self_collide>0</self_collide>
        <inertial>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>15.8079 -26.1939 0 0 -0 0</pose>
    </model>
    <model name='TrashBin'>
      <static>1</static>
      <link name='body'>
        <pose>0 0 0 0 -0 0</pose>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://TrashBin/meshes/TrashBin.obj</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://TrashBin/meshes/TrashBin.obj</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>14.577 -29.4685 0 0 -0 0</pose>
    </model>
    <model name='beer_clone'>
      <link name='link'>
        <pose>0 0 0.115 0 -0 0</pose>
        <inertial>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00058</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00058</iyy>
            <iyz>0</iyz>
            <izz>0.00019</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.055</radius>
              <length>0.23</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.055</radius>
              <length>0.23</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>model://beer/materials/scripts</uri>
              <uri>model://beer/materials/textures</uri>
              <name>Beer/Diffuse</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-3.11694 -11.8538 -0.811741 0.046694 0.014746 -0.015307</pose>
    </model>
    <model name='beer_clone_0'>
      <link name='link'>
        <pose>0 0 0.115 0 -0 0</pose>
        <inertial>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00058</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00058</iyy>
            <iyz>0</iyz>
            <izz>0.00019</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.055</radius>
              <length>0.23</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.055</radius>
              <length>0.23</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>model://beer/materials/scripts</uri>
              <uri>model://beer/materials/textures</uri>
              <name>Beer/Diffuse</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-4.71847 25.0114 -0.811756 0.04668 0.014745 -0.015449</pose>
    </model>
    <model name='master_chef_can_clone'>
      <link name='link'>
        <pose>0 0 0.07 0 -0 0</pose>
        <inertial>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00058</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00058</iyy>
            <iyz>0</iyz>
            <izz>0.00019</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <pose>0 0 -0.005 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.05</radius>
              <length>0.14</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>https://fuel.ignitionrobotics.org/1.0/petermitrano/models/master chef can/2/files/textured.obj</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-9.83479 11.9425 -0.848334 -0.048636 -0.005278 -2.95949</pose>
    </model>
    <model name='coke_can_2'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-11.6052 11.4665 0 0 -0 0</pose>
    </model>
    <model name='coke_can_3'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-13.75 15.9584 0 0 -0 0</pose>
    </model>
    <model name='coke_can_4'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-11.283 8.18685 0 0 -0 0</pose>
    </model>
    <model name='coke_can_5'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-29.5016 6.62129 0 0 -0 0</pose>
    </model>
    <model name='coke_can_6'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>10.6581 16.5289 0 0 -0 0</pose>
    </model>
    <model name='coke_can_7'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-15.0744 -7.39819 0 0 -0 0</pose>
    </model>
    <model name='coke_can_8'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>14.0887 -7.45318 0 0 -0 0</pose>
    </model>
    <model name='coke_can_9'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>12.1925 -0.993337 0 0 -0 0</pose>
    </model>
    <model name='coke_can_10'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>21.6863 2.38577 0 0 -0 0</pose>
    </model>
    <model name='coke_can_11'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.06 0 -0 0</pose>
          <mass>0.39</mass>
          <inertia>
            <ixx>0.00055575</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.00055575</iyy>
            <iyz>0</iyz>
            <izz>0.0001755</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0.003937 0.004724 -0.18 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://coke_can/meshes/coke_can.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>18.7989 2.16555 0 0 -0 0</pose>
    </model>
  </world>
</sdf>
