set(_CATKIN_CURRENT_PACKAGE "park_gazebo")
set(park_gazebo_VERSION "0.0.0")
set(park_gazebo_MAINTAINER "z <<EMAIL>>")
set(park_gazebo_PACKAGE_FORMAT "2")
set(park_gazebo_BUILD_DEPENDS "roscpp" "rospy" "std_msgs")
set(park_gazebo_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "std_msgs")
set(park_gazebo_BUILDTOOL_DEPENDS "catkin")
set(park_gazebo_BUILDTOOL_EXPORT_DEPENDS )
set(park_gazebo_EXEC_DEPENDS "roscpp" "rospy" "std_msgs")
set(park_gazebo_RUN_DEPENDS "roscpp" "rospy" "std_msgs")
set(park_gazebo_TEST_DEPENDS )
set(park_gazebo_DOC_DEPENDS )
set(park_gazebo_URL_WEBSITE "")
set(park_gazebo_URL_BUGTRACKER "")
set(park_gazebo_URL_REPOSITORY "")
set(park_gazebo_DEPRECATED "")