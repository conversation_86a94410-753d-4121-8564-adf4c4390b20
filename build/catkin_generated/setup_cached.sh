#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export CMAKE_PREFIX_PATH="/home/<USER>/my_gazebo_ws/devel:$CMAKE_PREFIX_PATH"
export LD_LIBRARY_PATH="/home/<USER>/my_gazebo_ws/devel/lib:$LD_LIBRARY_PATH"
export PWD='/home/<USER>/my_gazebo_ws/build'
export ROSLISP_PACKAGE_DIRECTORIES='/home/<USER>/my_gazebo_ws/devel/share/common-lisp'
export ROS_PACKAGE_PATH="/home/<USER>/my_gazebo_ws/src:$ROS_PACKAGE_PATH"